{"version": 3, "sources": ["../../wavesurfer.js/dist/plugins/hover.esm.js"], "sourcesContent": ["class t{constructor(){this.listeners={}}on(t,e,s){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),null==s?void 0:s.once){const s=()=>{this.un(t,s),this.un(t,e)};return this.on(t,s),s}return()=>this.un(t,e)}un(t,e){var s;null===(s=this.listeners[t])||void 0===s||s.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach((t=>t(...e)))}}class e extends t{constructor(t){super(),this.subscriptions=[],this.isDestroyed=!1,this.options=t}onInit(){}_init(t){this.isDestroyed&&(this.subscriptions=[],this.isDestroyed=!1),this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t())),this.subscriptions=[],this.isDestroyed=!0,this.wavesurfer=void 0}}function s(t,e){const i=e.xmlns?document.createElementNS(e.xmlns,t):document.createElement(t);for(const[t,n]of Object.entries(e))if(\"children\"===t&&n)for(const[t,e]of Object.entries(n))e instanceof Node?i.appendChild(e):\"string\"==typeof e?i.appendChild(document.createTextNode(e)):i.appendChild(s(t,e));else\"style\"===t?Object.assign(i.style,n):\"textContent\"===t?i.textContent=n:i.setAttribute(t,n.toString());return i}function i(t,e,i){const n=s(t,e||{});return null==i||i.appendChild(n),n}const n={lineWidth:1,labelSize:11,labelPreferLeft:!1,formatTimeCallback:t=>`${Math.floor(t/60)}:${`0${Math.floor(t)%60}`.slice(-2)}`};class o extends e{constructor(t){super(t||{}),this.lastPointerMove=null,this.unsubscribe=()=>{},this.onPointerMove=t=>{if(!this.wavesurfer)return;this.lastPointerMove=t;const e=this.wavesurfer.getWrapper().getBoundingClientRect(),{width:s}=e,i=t.clientX-e.left,n=Math.min(1,Math.max(0,i/s)),o=Math.min(s-this.options.lineWidth-1,i);this.wrapper.style.transform=`translateX(${o}px)`,this.wrapper.style.opacity=\"1\";const r=this.wavesurfer.getDuration()||0;this.label.textContent=this.options.formatTimeCallback(r*n);const a=this.label.offsetWidth,l=this.options.labelPreferLeft?o-a>0:o+a>s;this.label.style.transform=l?`translateX(-${a+this.options.lineWidth}px)`:\"\",this.emit(\"hover\",n)},this.onPointerLeave=()=>{this.wrapper.style.opacity=\"0\",this.lastPointerMove=null},this.options=Object.assign({},n,t),this.wrapper=i(\"div\",{part:\"hover\"}),this.label=i(\"span\",{part:\"hover-label\"},this.wrapper)}static create(t){return new o(t)}addUnits(t){return`${t}${\"number\"==typeof t?\"px\":\"\"}`}onInit(){if(!this.wavesurfer)throw Error(\"WaveSurfer is not initialized\");const t=this.wavesurfer.options,e=this.options.lineColor||t.cursorColor||t.progressColor;Object.assign(this.wrapper.style,{position:\"absolute\",zIndex:10,left:0,top:0,height:\"100%\",pointerEvents:\"none\",borderLeft:`${this.addUnits(this.options.lineWidth)} solid ${e}`,opacity:\"0\",transition:\"opacity .1s ease-in\"}),Object.assign(this.label.style,{display:\"block\",backgroundColor:this.options.labelBackground,color:this.options.labelColor,fontSize:`${this.addUnits(this.options.labelSize)}`,transition:\"transform .1s ease-in\",padding:\"2px 3px\"});const s=this.wavesurfer.getWrapper();s.appendChild(this.wrapper),s.addEventListener(\"pointermove\",this.onPointerMove),s.addEventListener(\"pointerleave\",this.onPointerLeave);const i=()=>{this.lastPointerMove&&this.onPointerMove(this.lastPointerMove)};this.wavesurfer.on(\"zoom\",i),this.wavesurfer.on(\"scroll\",i),this.unsubscribe=()=>{var t,e;s.removeEventListener(\"pointermove\",this.onPointerMove),s.removeEventListener(\"pointerleave\",this.onPointerLeave),null===(t=this.wavesurfer)||void 0===t||t.un(\"zoom\",i),null===(e=this.wavesurfer)||void 0===e||e.un(\"scroll\",i)}}destroy(){super.destroy(),this.unsubscribe(),this.wrapper.remove()}}export{o as default};\n"], "mappings": ";;;AAAA,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU,CAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAEC,IAAE;AAAC,QAAG,KAAK,UAAUF,EAAC,MAAI,KAAK,UAAUA,EAAC,IAAE,oBAAI,QAAK,KAAK,UAAUA,EAAC,EAAE,IAAIC,EAAC,GAAE,QAAMC,KAAE,SAAOA,GAAE,MAAK;AAAC,YAAMA,KAAE,MAAI;AAAC,aAAK,GAAGF,IAAEE,EAAC,GAAE,KAAK,GAAGF,IAAEC,EAAC;AAAA,MAAC;AAAE,aAAO,KAAK,GAAGD,IAAEE,EAAC,GAAEA;AAAA,IAAC;AAAC,WAAM,MAAI,KAAK,GAAGF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAE;AAAC,QAAIC;AAAE,cAAQA,KAAE,KAAK,UAAUF,EAAC,MAAI,WAASE,MAAGA,GAAE,OAAOD,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAEC,IAAE;AAAC,WAAO,KAAK,GAAGD,IAAEC,IAAE,EAAC,MAAK,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,YAAU,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,OAAKC,IAAE;AAAC,SAAK,UAAUD,EAAC,KAAG,KAAK,UAAUA,EAAC,EAAE,QAAS,CAAAA,OAAGA,GAAE,GAAGC,EAAC,CAAE;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAM,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,OAAG,KAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAE;AAAC,SAAK,gBAAc,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,QAAI,KAAK,aAAWA,IAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,KAAK,SAAS,GAAE,KAAK,cAAc,QAAS,CAAAA,OAAGA,GAAE,CAAE,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,MAAG,KAAK,aAAW;AAAA,EAAM;AAAC;AAAC,SAAS,EAAEA,IAAEC,IAAE;AAAC,QAAME,KAAEF,GAAE,QAAM,SAAS,gBAAgBA,GAAE,OAAMD,EAAC,IAAE,SAAS,cAAcA,EAAC;AAAE,aAAS,CAACA,IAAEI,EAAC,KAAI,OAAO,QAAQH,EAAC,EAAE,KAAG,eAAaD,MAAGI,GAAE,YAAS,CAACJ,IAAEC,EAAC,KAAI,OAAO,QAAQG,EAAC,EAAE,CAAAH,cAAa,OAAKE,GAAE,YAAYF,EAAC,IAAE,YAAU,OAAOA,KAAEE,GAAE,YAAY,SAAS,eAAeF,EAAC,CAAC,IAAEE,GAAE,YAAY,EAAEH,IAAEC,EAAC,CAAC;AAAA,MAAM,aAAUD,KAAE,OAAO,OAAOG,GAAE,OAAMC,EAAC,IAAE,kBAAgBJ,KAAEG,GAAE,cAAYC,KAAED,GAAE,aAAaH,IAAEI,GAAE,SAAS,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAEE,IAAE;AAAC,QAAMC,KAAE,EAAEJ,IAAEC,MAAG,CAAC,CAAC;AAAE,SAAO,QAAME,MAAGA,GAAE,YAAYC,EAAC,GAAEA;AAAC;AAAC,IAAM,IAAE,EAAC,WAAU,GAAE,WAAU,IAAG,iBAAgB,OAAG,oBAAmB,CAAAJ,OAAG,GAAG,KAAK,MAAMA,KAAE,EAAE,CAAC,IAAI,IAAI,KAAK,MAAMA,EAAC,IAAE,EAAE,GAAG,MAAM,EAAE,CAAC,GAAE;AAAE,IAAM,IAAN,MAAM,WAAU,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,MAAG,CAAC,CAAC,GAAE,KAAK,kBAAgB,MAAK,KAAK,cAAY,MAAI;AAAA,IAAC,GAAE,KAAK,gBAAc,CAAAA,OAAG;AAAC,UAAG,CAAC,KAAK,WAAW;AAAO,WAAK,kBAAgBA;AAAE,YAAMC,KAAE,KAAK,WAAW,WAAW,EAAE,sBAAsB,GAAE,EAAC,OAAMC,GAAC,IAAED,IAAEE,KAAEH,GAAE,UAAQC,GAAE,MAAKG,KAAE,KAAK,IAAI,GAAE,KAAK,IAAI,GAAED,KAAED,EAAC,CAAC,GAAEG,KAAE,KAAK,IAAIH,KAAE,KAAK,QAAQ,YAAU,GAAEC,EAAC;AAAE,WAAK,QAAQ,MAAM,YAAU,cAAcE,EAAC,OAAM,KAAK,QAAQ,MAAM,UAAQ;AAAI,YAAM,IAAE,KAAK,WAAW,YAAY,KAAG;AAAE,WAAK,MAAM,cAAY,KAAK,QAAQ,mBAAmB,IAAED,EAAC;AAAE,YAAM,IAAE,KAAK,MAAM,aAAY,IAAE,KAAK,QAAQ,kBAAgBC,KAAE,IAAE,IAAEA,KAAE,IAAEH;AAAE,WAAK,MAAM,MAAM,YAAU,IAAE,eAAe,IAAE,KAAK,QAAQ,SAAS,QAAM,IAAG,KAAK,KAAK,SAAQE,EAAC;AAAA,IAAC,GAAE,KAAK,iBAAe,MAAI;AAAC,WAAK,QAAQ,MAAM,UAAQ,KAAI,KAAK,kBAAgB;AAAA,IAAI,GAAE,KAAK,UAAQ,OAAO,OAAO,CAAC,GAAE,GAAEJ,EAAC,GAAE,KAAK,UAAQ,EAAE,OAAM,EAAC,MAAK,QAAO,CAAC,GAAE,KAAK,QAAM,EAAE,QAAO,EAAC,MAAK,cAAa,GAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,OAAO,OAAOA,IAAE;AAAC,WAAO,IAAI,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,WAAM,GAAGA,EAAC,GAAG,YAAU,OAAOA,KAAE,OAAK,EAAE;AAAA,EAAE;AAAA,EAAC,SAAQ;AAAC,QAAG,CAAC,KAAK,WAAW,OAAM,MAAM,+BAA+B;AAAE,UAAMA,KAAE,KAAK,WAAW,SAAQC,KAAE,KAAK,QAAQ,aAAWD,GAAE,eAAaA,GAAE;AAAc,WAAO,OAAO,KAAK,QAAQ,OAAM,EAAC,UAAS,YAAW,QAAO,IAAG,MAAK,GAAE,KAAI,GAAE,QAAO,QAAO,eAAc,QAAO,YAAW,GAAG,KAAK,SAAS,KAAK,QAAQ,SAAS,CAAC,UAAUC,EAAC,IAAG,SAAQ,KAAI,YAAW,sBAAqB,CAAC,GAAE,OAAO,OAAO,KAAK,MAAM,OAAM,EAAC,SAAQ,SAAQ,iBAAgB,KAAK,QAAQ,iBAAgB,OAAM,KAAK,QAAQ,YAAW,UAAS,GAAG,KAAK,SAAS,KAAK,QAAQ,SAAS,CAAC,IAAG,YAAW,yBAAwB,SAAQ,UAAS,CAAC;AAAE,UAAMC,KAAE,KAAK,WAAW,WAAW;AAAE,IAAAA,GAAE,YAAY,KAAK,OAAO,GAAEA,GAAE,iBAAiB,eAAc,KAAK,aAAa,GAAEA,GAAE,iBAAiB,gBAAe,KAAK,cAAc;AAAE,UAAMC,KAAE,MAAI;AAAC,WAAK,mBAAiB,KAAK,cAAc,KAAK,eAAe;AAAA,IAAC;AAAE,SAAK,WAAW,GAAG,QAAOA,EAAC,GAAE,KAAK,WAAW,GAAG,UAASA,EAAC,GAAE,KAAK,cAAY,MAAI;AAAC,UAAIH,IAAEC;AAAE,MAAAC,GAAE,oBAAoB,eAAc,KAAK,aAAa,GAAEA,GAAE,oBAAoB,gBAAe,KAAK,cAAc,GAAE,UAAQF,KAAE,KAAK,eAAa,WAASA,MAAGA,GAAE,GAAG,QAAOG,EAAC,GAAE,UAAQF,KAAE,KAAK,eAAa,WAASA,MAAGA,GAAE,GAAG,UAASE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,YAAY,GAAE,KAAK,QAAQ,OAAO;AAAA,EAAC;AAAC;", "names": ["t", "e", "s", "i", "n", "o"]}