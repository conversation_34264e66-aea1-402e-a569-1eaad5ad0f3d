import os
import json
import requests
import mimetypes
import time
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

# 配置部分
class Config:
    # 音频文件路径 - 请修改为你的音频文件路径
    AUDIO_PATH = r"C:\Users\<USER>\Desktop\格式转换\28号.mp3"

    # Gemini API密钥 - 请替换为你的实际API密钥
    GEMINI_API_KEY = "AIzaSyAYF_flfuyGcxorcbda5DJ2cDZpTEyBZ34"

    # 显示名称
    DISPLAY_NAME = "AUDIO"

    # 网络配置
    MAX_RETRIES = 3
    BACKOFF_FACTOR = 1
    TIMEOUT = 30

    # SSL配置 - 如果遇到SSL问题可以设置为False（不推荐生产环境）
    VERIFY_SSL = True

    # 代理配置 - 如果遇到地区限制可以配置代理
    # 格式: {"http": "http://proxy:port", "https": "https://proxy:port"}
    PROXIES = None

    # 备用API端点 - 某些地区可能需要使用不同的端点
    API_BASE_URL = "https://zhanghaiwen.deno.dev/gemini"

class GeminiSpeechToText:
    """Gemini语音转文字工具类"""

    def __init__(self, api_key=None, verify_ssl=True, proxies=None):
        """
        初始化Gemini语音转文字客户端

        Args:
            api_key: Gemini API密钥，如果不提供则使用Config中的默认值
            verify_ssl: 是否验证SSL证书
            proxies: 代理配置，格式: {"http": "http://proxy:port", "https": "https://proxy:port"}
        """
        self.api_key = api_key or Config.GEMINI_API_KEY
        self.verify_ssl = verify_ssl
        self.proxies = proxies or Config.PROXIES
        self.session = self._create_session()

    def _create_session(self):
        """创建带有重试机制的requests会话"""
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=Config.MAX_RETRIES,
            backoff_factor=Config.BACKOFF_FACTOR,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    def _get_file_info(self, audio_path):
        """获取音频文件信息"""
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(audio_path)
        if mime_type is None:
            # 根据文件扩展名推断MIME类型
            ext = os.path.splitext(audio_path)[1].lower()
            mime_map = {
                '.mp3': 'audio/mpeg',
                '.wav': 'audio/wav',
                '.m4a': 'audio/mp4',
                '.flac': 'audio/flac',
                '.ogg': 'audio/ogg'
            }
            mime_type = mime_map.get(ext, 'application/octet-stream')

        # 获取文件大小
        file_size = os.path.getsize(audio_path)

        return mime_type, file_size

    def upload_audio_file(self, audio_path, display_name="AUDIO"):
        """
        上传音频文件到Gemini

        Args:
            audio_path: 音频文件路径
            display_name: 显示名称

        Returns:
            str: 上传后的文件URI
        """
        print(f"🎵 开始上传音频文件: {audio_path}")

        # 获取文件信息
        mime_type, file_size = self._get_file_info(audio_path)
        print(f"📁 文件信息: {mime_type}, {file_size} bytes")

        # 第一步：初始化上传
        headers = {
            "x-goog-api-key": self.api_key,
            "X-Goog-Upload-Protocol": "resumable",
            "X-Goog-Upload-Command": "start",
            "X-Goog-Upload-Header-Content-Length": str(file_size),
            "X-Goog-Upload-Header-Content-Type": mime_type,
            "Content-Type": "application/json"
        }

        data = {
            "file": {
                "display_name": display_name
            }
        }

        try:
            response = self.session.post(
                f"{Config.API_BASE_URL}/upload/v1beta/files",
                headers=headers,
                json=data,
                timeout=Config.TIMEOUT,
                verify=self.verify_ssl,
                proxies=self.proxies
            )

            # 详细的错误信息
            if response.status_code != 200:
                print(f"❌ HTTP错误 {response.status_code}: {response.reason}")
                print(f"📄 响应内容: {response.text}")

                # 特殊处理地区限制错误
                if "User location is not supported" in response.text:
                    print("\n🌍 地区限制解决方案:")
                    print("1. 使用VPN连接到支持的地区（如美国、欧洲等）")
                    print("2. 配置HTTP代理服务器")
                    print("3. 使用其他语音识别服务（如OpenAI Whisper、百度语音等）")
                    print("\n💡 如需配置代理，请修改Config.PROXIES设置")

            response.raise_for_status()

            # 获取上传URL
            upload_url = response.headers.get("X-Goog-Upload-Url")
            if not upload_url:
                raise ValueError("上传URL未在响应头中找到")

            print("✅ 初始化上传成功，开始上传文件...")

            # 第二步：上传实际文件
            upload_headers = {
                "Content-Length": str(file_size),
                "X-Goog-Upload-Offset": "0",
                "X-Goog-Upload-Command": "upload, finalize"
            }

            with open(audio_path, 'rb') as f:
                upload_response = self.session.post(
                    upload_url,
                    headers=upload_headers,
                    data=f,
                    timeout=Config.TIMEOUT,
                    verify=self.verify_ssl,
                    proxies=self.proxies
                )
                upload_response.raise_for_status()

            file_info = upload_response.json()
            file_uri = file_info.get("file", {}).get("uri")
            if not file_uri:
                raise ValueError("文件URI未在响应中找到")

            print(f"🎉 文件上传成功! URI: {file_uri}")
            return file_uri, mime_type

        except requests.exceptions.SSLError as e:
            print(f"❌ SSL错误: {e}")
            print("💡 建议: 尝试设置 verify_ssl=False 或检查网络连接")
            raise
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
            print("💡 建议: 检查网络连接或代理设置")
            raise
        except requests.exceptions.Timeout as e:
            print(f"❌ 请求超时: {e}")
            print("💡 建议: 增加超时时间或检查网络速度")
            raise
        except Exception as e:
            print(f"❌ 上传失败: {e}")
            raise

    def speech_to_text(self, file_uri, mime_type, prompt="请将这段音频转换为文字"):
        """
        使用Gemini进行语音转文字

        Args:
            file_uri: 上传后的文件URI
            mime_type: 文件MIME类型
            prompt: 提示词

        Returns:
            str: 识别出的文字内容
        """
        print(f"🎤 开始语音转文字识别...")

        headers = {
            "x-goog-api-key": self.api_key,
            "Content-Type": "application/json"
        }

        data = {
            "contents": [{
                "parts": [
                    {"text": prompt},
                    {"file_data": {"mime_type": mime_type, "file_uri": file_uri}}
                ]
            }]
        }

        try:
            response = self.session.post(
                f"{Config.API_BASE_URL}/v1beta/models/gemini-2.5-flash:generateContent",
                headers=headers,
                json=data,
                timeout=Config.TIMEOUT,
                verify=self.verify_ssl,
                proxies=self.proxies
            )
            response.raise_for_status()

            response_json = response.json()

            # 提取文字内容
            text_results = []
            for candidate in response_json.get("candidates", []):
                for part in candidate.get("content", {}).get("parts", []):
                    if "text" in part:
                        text_results.append(part["text"])

            if text_results:
                result_text = "\n".join(text_results)
                print(f"✅ 识别成功!")
                return result_text
            else:
                print("⚠️ 未找到识别结果")
                return ""

        except Exception as e:
            print(f"❌ 语音识别失败: {e}")
            raise

    def convert_audio_to_text(self, audio_path, prompt="请将这段音频转换为文字"):
        """
        完整的语音转文字流程

        Args:
            audio_path: 音频文件路径
            prompt: 提示词

        Returns:
            str: 识别出的文字内容
        """
        try:
            # 上传文件
            file_uri, mime_type = self.upload_audio_file(audio_path)

            # 等待一下确保文件处理完成
            time.sleep(2)

            # 进行语音识别
            result = self.speech_to_text(file_uri, mime_type, prompt)

            return result

        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return ""


def main():
    """主函数 - 使用示例"""
    print("🎵 Gemini语音转文字工具")
    print("=" * 50)

    # 创建客户端
    # 如果遇到SSL问题，可以设置 verify_ssl=False
    client = GeminiSpeechToText(verify_ssl=Config.VERIFY_SSL)

    # 检查音频文件是否存在
    if not os.path.exists(Config.AUDIO_PATH):
        print(f"❌ 音频文件不存在: {Config.AUDIO_PATH}")
        print("💡 请修改 Config.AUDIO_PATH 为正确的音频文件路径")
        return

    print(f"📁 音频文件: {Config.AUDIO_PATH}")

    # 执行语音转文字
    result = client.convert_audio_to_text(
        Config.AUDIO_PATH,
        prompt="请将这段音频转换为中文文字，保持原意和语调"
    )

    if result:
        print("\n" + "=" * 50)
        print("📝 识别结果:")
        print("-" * 50)
        print(result)
        print("=" * 50)
    else:
        print("❌ 未能识别出任何文字内容")


if __name__ == "__main__":
    main()