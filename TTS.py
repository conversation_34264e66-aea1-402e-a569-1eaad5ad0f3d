import os
import json
import requests
import mimetypes

# 设置变量
AUDIO_PATH = "path/to/sample.mp3"
GEMINI_API_KEY = "YOUR_GEMINI_API_KEY"  # 需要替换为实际的API密钥
DISPLAY_NAME = "AUDIO"

# 获取MIME类型和文件大小
MIME_TYPE, _ = mimetypes.guess_type(AUDIO_PATH)
if MIME_TYPE is None:
    # 如果无法猜测MIME类型，则使用默认值
    MIME_TYPE = "application/octet-stream"
NUM_BYTES = os.path.getsize(AUDIO_PATH)

# 初始可恢复请求，定义元数据
headers = {
    "x-goog-api-key": GEMINI_API_KEY,
    "X-Goog-Upload-Protocol": "resumable",
    "X-Goog-Upload-Command": "start",
    "X-Goog-Upload-Header-Content-Length": str(NUM_BYTES),
    "X-Goog-Upload-Header-Content-Type": MIME_TYPE,
    "Content-Type": "application/json"
}

data = {
    "file": {
        "display_name": DISPLAY_NAME
    }
}

try:
    response = requests.post(
        "https://generativelanguage.googleapis.com/upload/v1beta/files",
        headers=headers,
        json=data
    )
    response.raise_for_status()  # 如果请求失败，抛出异常

    # 从响应头中获取上传URL
    upload_url = response.headers.get("X-Goog-Upload-Url")
    if not upload_url:
        raise ValueError("Upload URL not found in response headers")

    # 上传实际的文件字节
    headers = {
        "Content-Length": str(NUM_BYTES),
        "X-Goog-Upload-Offset": "0",
        "X-Goog-Upload-Command": "upload, finalize"
    }

    with open(AUDIO_PATH, 'rb') as f:
        response = requests.post(
            upload_url,
            headers=headers,
            data=f
        )
        response.raise_for_status()

    file_info = response.json()
    file_uri = file_info.get("file", {}).get("uri")
    if not file_uri:
        raise ValueError("File URI not found in response")
    print(f"file_uri={file_uri}")

    # 使用该文件生成内容
    headers = {
        "x-goog-api-key": GEMINI_API_KEY,
        "Content-Type": "application/json"
    }

    data = {
        "contents": [{
            "parts": [
                {"text": "Describe this audio clip"},
                {"file_data": {"mime_type": MIME_TYPE, "file_uri": file_uri}}
            ]
        }]
    }

    response = requests.post(
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent",
        headers=headers,
        json=data
    )
    response.raise_for_status()

    response_json = response.json()
    print(json.dumps(response_json, indent=2))

    # 提取并打印生成的文本
    for candidate in response_json.get("candidates", []):
        for part in candidate.get("content", {}).get("parts", []):
            if "text" in part:
                print(part["text"])

except requests.exceptions.RequestException as e:
    print(f"Request failed: {e}")
except ValueError as e:
    print(f"Value error: {e}")
except Exception as e:
    print(f"An unexpected error occurred: {e}")