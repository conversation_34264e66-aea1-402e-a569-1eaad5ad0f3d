{"version": 3, "sources": ["../../wavesurfer.js/dist/plugins/zoom.esm.js"], "sourcesContent": ["class t{constructor(){this.listeners={}}on(t,s,e){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(s),null==e?void 0:e.once){const e=()=>{this.un(t,e),this.un(t,s)};return this.on(t,e),e}return()=>this.un(t,s)}un(t,s){var e;null===(e=this.listeners[t])||void 0===e||e.delete(s)}once(t,s){return this.on(t,s,{once:!0})}unAll(){this.listeners={}}emit(t,...s){this.listeners[t]&&this.listeners[t].forEach((t=>t(...s)))}}class s extends t{constructor(t){super(),this.subscriptions=[],this.isDestroyed=!1,this.options=t}onInit(){}_init(t){this.isDestroyed&&(this.subscriptions=[],this.isDestroyed=!1),this.wavesurfer=t,this.onInit()}destroy(){this.emit(\"destroy\"),this.subscriptions.forEach((t=>t())),this.subscriptions=[],this.isDestroyed=!0,this.wavesurfer=void 0}}const e={scale:.5,deltaThreshold:5,exponentialZooming:!1,iterations:20};class i extends s{constructor(t){super(t||{}),this.wrapper=void 0,this.container=null,this.accumulatedDelta=0,this.pointerTime=0,this.oldX=0,this.endZoom=0,this.startZoom=0,this.onWheel=t=>{if(this.wavesurfer&&this.container&&!(Math.abs(t.deltaX)>=Math.abs(t.deltaY))&&(t.preventDefault(),this.accumulatedDelta+=-t.deltaY,0===this.startZoom&&this.options.exponentialZooming&&(this.startZoom=this.wavesurfer.getWrapper().clientWidth/this.wavesurfer.getDuration()),0===this.options.deltaThreshold||Math.abs(this.accumulatedDelta)>=this.options.deltaThreshold)){const s=this.wavesurfer.getDuration(),e=0===this.wavesurfer.options.minPxPerSec?this.wavesurfer.getWrapper().scrollWidth/s:this.wavesurfer.options.minPxPerSec,i=t.clientX-this.container.getBoundingClientRect().left,o=this.container.clientWidth,n=this.wavesurfer.getScroll();i===this.oldX&&0!==this.oldX||(this.pointerTime=(n+i)/e),this.oldX=i;const r=this.calculateNewZoom(e,this.accumulatedDelta),h=o/r*(i/o);r*s<o?(this.wavesurfer.zoom(o/s),this.container.scrollLeft=0):(this.wavesurfer.zoom(r),this.container.scrollLeft=(this.pointerTime-h)*r),this.accumulatedDelta=0}},this.calculateNewZoom=(t,s)=>{let e;if(this.options.exponentialZooming){const i=s>0?Math.pow(this.endZoom/this.startZoom,1/(this.options.iterations-1)):Math.pow(this.startZoom/this.endZoom,1/(this.options.iterations-1));e=Math.max(0,t*i)}else e=Math.max(0,t+s*this.options.scale);return Math.min(e,this.options.maxZoom)},this.options=Object.assign({},e,t)}static create(t){return new i(t)}onInit(){var t;this.wrapper=null===(t=this.wavesurfer)||void 0===t?void 0:t.getWrapper(),this.wrapper&&(this.container=this.wrapper.parentElement,this.container.addEventListener(\"wheel\",this.onWheel),void 0===this.options.maxZoom&&(this.options.maxZoom=this.container.clientWidth),this.endZoom=this.options.maxZoom)}destroy(){this.wrapper&&this.wrapper.removeEventListener(\"wheel\",this.onWheel),super.destroy()}}export{i as default};\n"], "mappings": ";;;AAAA,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,YAAU,CAAC;AAAA,EAAC;AAAA,EAAC,GAAGA,IAAEC,IAAEC,IAAE;AAAC,QAAG,KAAK,UAAUF,EAAC,MAAI,KAAK,UAAUA,EAAC,IAAE,oBAAI,QAAK,KAAK,UAAUA,EAAC,EAAE,IAAIC,EAAC,GAAE,QAAMC,KAAE,SAAOA,GAAE,MAAK;AAAC,YAAMA,KAAE,MAAI;AAAC,aAAK,GAAGF,IAAEE,EAAC,GAAE,KAAK,GAAGF,IAAEC,EAAC;AAAA,MAAC;AAAE,aAAO,KAAK,GAAGD,IAAEE,EAAC,GAAEA;AAAA,IAAC;AAAC,WAAM,MAAI,KAAK,GAAGF,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,GAAGD,IAAEC,IAAE;AAAC,QAAIC;AAAE,cAAQA,KAAE,KAAK,UAAUF,EAAC,MAAI,WAASE,MAAGA,GAAE,OAAOD,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAEC,IAAE;AAAC,WAAO,KAAK,GAAGD,IAAEC,IAAE,EAAC,MAAK,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,YAAU,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKD,OAAKC,IAAE;AAAC,SAAK,UAAUD,EAAC,KAAG,KAAK,UAAUA,EAAC,EAAE,QAAS,CAAAA,OAAGA,GAAE,GAAGC,EAAC,CAAE;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAM,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,OAAG,KAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAE;AAAC,SAAK,gBAAc,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,QAAI,KAAK,aAAWA,IAAE,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,KAAK,SAAS,GAAE,KAAK,cAAc,QAAS,CAAAA,OAAGA,GAAE,CAAE,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,MAAG,KAAK,aAAW;AAAA,EAAM;AAAC;AAAC,IAAM,IAAE,EAAC,OAAM,KAAG,gBAAe,GAAE,oBAAmB,OAAG,YAAW,GAAE;AAAE,IAAM,IAAN,MAAM,WAAU,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMA,MAAG,CAAC,CAAC,GAAE,KAAK,UAAQ,QAAO,KAAK,YAAU,MAAK,KAAK,mBAAiB,GAAE,KAAK,cAAY,GAAE,KAAK,OAAK,GAAE,KAAK,UAAQ,GAAE,KAAK,YAAU,GAAE,KAAK,UAAQ,CAAAA,OAAG;AAAC,UAAG,KAAK,cAAY,KAAK,aAAW,EAAE,KAAK,IAAIA,GAAE,MAAM,KAAG,KAAK,IAAIA,GAAE,MAAM,OAAKA,GAAE,eAAe,GAAE,KAAK,oBAAkB,CAACA,GAAE,QAAO,MAAI,KAAK,aAAW,KAAK,QAAQ,uBAAqB,KAAK,YAAU,KAAK,WAAW,WAAW,EAAE,cAAY,KAAK,WAAW,YAAY,IAAG,MAAI,KAAK,QAAQ,kBAAgB,KAAK,IAAI,KAAK,gBAAgB,KAAG,KAAK,QAAQ,iBAAgB;AAAC,cAAMC,KAAE,KAAK,WAAW,YAAY,GAAEC,KAAE,MAAI,KAAK,WAAW,QAAQ,cAAY,KAAK,WAAW,WAAW,EAAE,cAAYD,KAAE,KAAK,WAAW,QAAQ,aAAYE,KAAEH,GAAE,UAAQ,KAAK,UAAU,sBAAsB,EAAE,MAAK,IAAE,KAAK,UAAU,aAAY,IAAE,KAAK,WAAW,UAAU;AAAE,QAAAG,OAAI,KAAK,QAAM,MAAI,KAAK,SAAO,KAAK,eAAa,IAAEA,MAAGD,KAAG,KAAK,OAAKC;AAAE,cAAM,IAAE,KAAK,iBAAiBD,IAAE,KAAK,gBAAgB,GAAE,IAAE,IAAE,KAAGC,KAAE;AAAG,YAAEF,KAAE,KAAG,KAAK,WAAW,KAAK,IAAEA,EAAC,GAAE,KAAK,UAAU,aAAW,MAAI,KAAK,WAAW,KAAK,CAAC,GAAE,KAAK,UAAU,cAAY,KAAK,cAAY,KAAG,IAAG,KAAK,mBAAiB;AAAA,MAAC;AAAA,IAAC,GAAE,KAAK,mBAAiB,CAACD,IAAEC,OAAI;AAAC,UAAIC;AAAE,UAAG,KAAK,QAAQ,oBAAmB;AAAC,cAAMC,KAAEF,KAAE,IAAE,KAAK,IAAI,KAAK,UAAQ,KAAK,WAAU,KAAG,KAAK,QAAQ,aAAW,EAAE,IAAE,KAAK,IAAI,KAAK,YAAU,KAAK,SAAQ,KAAG,KAAK,QAAQ,aAAW,EAAE;AAAE,QAAAC,KAAE,KAAK,IAAI,GAAEF,KAAEG,EAAC;AAAA,MAAC,MAAM,CAAAD,KAAE,KAAK,IAAI,GAAEF,KAAEC,KAAE,KAAK,QAAQ,KAAK;AAAE,aAAO,KAAK,IAAIC,IAAE,KAAK,QAAQ,OAAO;AAAA,IAAC,GAAE,KAAK,UAAQ,OAAO,OAAO,CAAC,GAAE,GAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,OAAOA,IAAE;AAAC,WAAO,IAAI,GAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAIA;AAAE,SAAK,UAAQ,UAAQA,KAAE,KAAK,eAAa,WAASA,KAAE,SAAOA,GAAE,WAAW,GAAE,KAAK,YAAU,KAAK,YAAU,KAAK,QAAQ,eAAc,KAAK,UAAU,iBAAiB,SAAQ,KAAK,OAAO,GAAE,WAAS,KAAK,QAAQ,YAAU,KAAK,QAAQ,UAAQ,KAAK,UAAU,cAAa,KAAK,UAAQ,KAAK,QAAQ;AAAA,EAAQ;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,KAAK,QAAQ,oBAAoB,SAAQ,KAAK,OAAO,GAAE,MAAM,QAAQ;AAAA,EAAC;AAAC;", "names": ["t", "s", "e", "i"]}