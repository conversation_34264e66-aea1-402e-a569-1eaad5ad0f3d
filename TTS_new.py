import os
import json
import base64
import mimetypes
import requests

# 配置部分
class Config:
    # 音频文件路径 - 请修改为你的音频文件路径
    AUDIO_PATH = r"C:\Users\<USER>\Desktop\格式转换\28号.mp3"
    
    # Gemini API密钥 - 请替换为你的实际API密钥
    GEMINI_API_KEY = "AIzaSyAYF_flfuyGcxorcbda5DJ2cDZpTEyBZ34"


class GeminiSpeechToText:
    """Gemini语音转文字工具类 - 使用内嵌音频数据"""
    
    def __init__(self, api_key=None):
        """
        初始化Gemini语音转文字客户端
        
        Args:
            api_key: Gemini API密钥，如果不提供则使用Config中的默认值
        """
        self.api_key = api_key or Config.GEMINI_API_KEY
    
    def _get_file_info(self, audio_path):
        """获取音频文件信息"""
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(audio_path)
        if mime_type is None:
            # 根据文件扩展名推断MIME类型
            ext = os.path.splitext(audio_path)[1].lower()
            mime_map = {
                '.mp3': 'audio/mpeg',
                '.wav': 'audio/wav',
                '.m4a': 'audio/mp4',
                '.flac': 'audio/flac',
                '.ogg': 'audio/ogg'
            }
            mime_type = mime_map.get(ext, 'audio/mpeg')
        
        return mime_type
    
    def _encode_audio_to_base64(self, audio_path):
        """将音频文件编码为base64"""
        print(f"📁 读取音频文件: {audio_path}")
        
        with open(audio_path, 'rb') as audio_file:
            audio_bytes = audio_file.read()
            audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
        
        print(f"✅ 音频文件编码完成，大小: {len(audio_bytes)} bytes")
        return audio_base64
    
    def speech_to_text(self, audio_path, prompt="请将这段音频转换为中文文字"):
        """
        使用Gemini进行语音转文字 - 内嵌音频数据方式
        
        Args:
            audio_path: 音频文件路径
            prompt: 提示词
            
        Returns:
            str: 识别出的文字内容
        """
        print(f"🎤 开始语音转文字识别...")
        
        try:
            # 获取文件信息
            mime_type = self._get_file_info(audio_path)
            print(f"📄 文件类型: {mime_type}")
            
            # 编码音频文件
            audio_base64 = self._encode_audio_to_base64(audio_path)
            
            # 构建请求
            headers = {
                "x-goog-api-key": self.api_key,
                "Content-Type": "application/json"
            }
            
            # 使用内嵌音频数据的请求格式
            data = {
                "contents": [{
                    "parts": [
                        {"text": prompt},
                        {
                            "inline_data": {
                                "mime_type": mime_type,
                                "data": audio_base64
                            }
                        }
                    ]
                }]
            }
            
            print("🚀 发送识别请求...")
            
            # 发送请求
            response = requests.post(
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent",
                headers=headers,
                json=data,
                timeout=60
            )
            
            # 检查响应
            if response.status_code != 200:
                print(f"❌ HTTP错误 {response.status_code}: {response.reason}")
                print(f"📄 响应内容: {response.text}")
                
                # 特殊处理地区限制错误
                if "User location is not supported" in response.text:
                    print("\n🌍 地区限制解决方案:")
                    print("1. 使用VPN连接到支持的地区（如美国、欧洲等）")
                    print("2. 使用其他语音识别服务（如OpenAI Whisper、百度语音等）")
                
                return ""
            
            response_json = response.json()
            
            # 提取文字内容
            text_results = []
            for candidate in response_json.get("candidates", []):
                for part in candidate.get("content", {}).get("parts", []):
                    if "text" in part:
                        text_results.append(part["text"])
            
            if text_results:
                result_text = "\n".join(text_results)
                print(f"✅ 识别成功!")
                return result_text
            else:
                print("⚠️ 未找到识别结果")
                return ""
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return ""
        except Exception as e:
            print(f"❌ 语音识别失败: {e}")
            return ""


def main():
    """主函数 - 使用示例"""
    print("🎵 Gemini语音转文字工具 (内嵌音频数据版)")
    print("=" * 60)
    
    # 创建客户端
    client = GeminiSpeechToText()
    
    # 检查音频文件是否存在
    if not os.path.exists(Config.AUDIO_PATH):
        print(f"❌ 音频文件不存在: {Config.AUDIO_PATH}")
        print("💡 请修改 Config.AUDIO_PATH 为正确的音频文件路径")
        return
    
    print(f"📁 音频文件: {Config.AUDIO_PATH}")
    
    # 执行语音转文字
    result = client.speech_to_text(
        Config.AUDIO_PATH, 
        prompt="请将这段音频转换为中文文字，保持原意和语调"
    )
    
    if result:
        print("\n" + "=" * 60)
        print("📝 识别结果:")
        print("-" * 60)
        print(result)
        print("=" * 60)
    else:
        print("❌ 未能识别出任何文字内容")


if __name__ == "__main__":
    main()
