import type { CSSProperties } from 'vue';
import type { RowEventHandlers, TableV2RowProps } from '../row';
declare const TableV2Row: import("vue").DefineComponent<{
    readonly class: StringConstructor;
    readonly columns: {
        readonly type: import("vue").PropType<import("../common").AnyColumn[]>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly columnsStyles: {
        readonly type: import("vue").PropType<Record<import("../types").KeyType, CSSProperties>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly depth: NumberConstructor;
    readonly expandColumnKey: StringConstructor;
    readonly estimatedRowHeight: {
        readonly default: undefined;
        readonly type: import("vue").PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        readonly __epPropKey: true;
    };
    readonly isScrolling: BooleanConstructor;
    readonly onRowExpand: {
        readonly type: import("vue").PropType<import("../row").RowExpandHandler>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onRowHover: {
        readonly type: import("vue").PropType<import("../row").RowHoverHandler>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onRowHeightChange: {
        readonly type: import("vue").PropType<import("../row").RowHeightChangeHandler>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly rowData: {
        readonly type: import("vue").PropType<any>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly rowEventHandlers: {
        readonly type: import("vue").PropType<RowEventHandlers>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly rowIndex: {
        readonly type: import("vue").PropType<number>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly rowKey: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string | number | symbol) | (() => import("../types").KeyType) | ((new (...args: any[]) => string | number | symbol) | (() => import("../types").KeyType))[], unknown, unknown, "id", boolean>;
    readonly style: {
        readonly type: import("vue").PropType<CSSProperties>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}, () => JSX.Element, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly class: StringConstructor;
    readonly columns: {
        readonly type: import("vue").PropType<import("../common").AnyColumn[]>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly columnsStyles: {
        readonly type: import("vue").PropType<Record<import("../types").KeyType, CSSProperties>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly depth: NumberConstructor;
    readonly expandColumnKey: StringConstructor;
    readonly estimatedRowHeight: {
        readonly default: undefined;
        readonly type: import("vue").PropType<number>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        readonly __epPropKey: true;
    };
    readonly isScrolling: BooleanConstructor;
    readonly onRowExpand: {
        readonly type: import("vue").PropType<import("../row").RowExpandHandler>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onRowHover: {
        readonly type: import("vue").PropType<import("../row").RowHoverHandler>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly onRowHeightChange: {
        readonly type: import("vue").PropType<import("../row").RowHeightChangeHandler>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly rowData: {
        readonly type: import("vue").PropType<any>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly rowEventHandlers: {
        readonly type: import("vue").PropType<RowEventHandlers>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly rowIndex: {
        readonly type: import("vue").PropType<number>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly rowKey: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => string | number | symbol) | (() => import("../types").KeyType) | ((new (...args: any[]) => string | number | symbol) | (() => import("../types").KeyType))[], unknown, unknown, "id", boolean>;
    readonly style: {
        readonly type: import("vue").PropType<CSSProperties>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
}>>, {
    readonly rowKey: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => string | number | symbol) | (() => import("../types").KeyType) | ((new (...args: any[]) => string | number | symbol) | (() => import("../types").KeyType))[], unknown, unknown>;
    readonly estimatedRowHeight: number;
    readonly isScrolling: boolean;
}>;
export default TableV2Row;
export type TableV2RowCellRenderParam = {
    column: TableV2RowProps['columns'][number];
    columns: TableV2RowProps['columns'];
    columnIndex: number;
    depth: number;
    style: CSSProperties;
    rowData: any;
    rowIndex: number;
    isScrolling: boolean;
    expandIconProps?: {
        rowData: any;
        rowIndex: number;
        onExpand: (expand: boolean) => void;
    };
};
